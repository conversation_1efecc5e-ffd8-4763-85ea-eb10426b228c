<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Reactivación Mina Esperanza III</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(45deg, #1a237e, #283593);
            color: white;
            padding: 30px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .metrics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .metric-card.production { border-left-color: #e74c3c; }
        .metric-card.investment { border-left-color: #3498db; }
        .metric-card.profit { border-left-color: #2ecc71; }
        .metric-card.efficiency { border-left-color: #f39c12; }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .metric-label {
            font-size: 1.1em;
            color: #666;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .chart-container:hover {
            transform: translateY(-3px);
        }
        
        .chart-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .implementation-timeline {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-left: 3px solid #3498db;
            margin-left: 15px;
            padding-left: 20px;
            position: relative;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            width: 12px;
            height: 12px;
            background: #3498db;
            border-radius: 50%;
        }
        
        .week-number {
            background: #3498db;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 15px;
            min-width: 60px;
            text-align: center;
        }
        
        .action-text {
            flex: 1;
            font-weight: 500;
        }
        
        .responsible {
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 0.9em;
            color: #2c3e50;
        }
        
        .conclusions {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .conclusions h3 {
            font-size: 1.6em;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .conclusion-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .conclusion-point {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .conclusion-point h4 {
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1> UNIDAD MUKI - MINA ESPERANZA III -  - UNDER<span style="color: black; font-size: 0.7em;">◆</span></h1>
        <p>Propuesta de Reactivación Integral | Ing. Mucio Lima - Gerente General</p>
        <p><strong>Elaborado por:</strong> Hitler K. Natividad Santamaria - Responsable RR.HH.</p>
    </div>

    <div class="container">
        <!-- Métricas Clave -->
        <div class="metrics-overview">
            <div class="metric-card production">
                <div class="metric-value" style="color: #e74c3c;">60 TN</div>
                <div class="metric-label">Producción Objetivo Diaria</div>
            </div>
            <div class="metric-card investment">
                <div class="metric-value" style="color: #3498db;">$201.6K</div>
                <div class="metric-label">Inversión Inicial (S/)</div>
            </div>
            <div class="metric-card profit">
                <div class="metric-value" style="color: #2ecc71;">$323K</div>
                <div class="metric-label">Utilidad Mensual (USD)</div>
            </div>
            <div class="metric-card efficiency">
                <div class="metric-value" style="color: #f39c12;">67.1%</div>
                <div class="metric-label">Margen de Utilidad</div>
            </div>
        </div>

        <!-- Gráficos Principales -->
        <div class="charts-grid">
            <!-- Gráfico de Producción Comparativa -->
            <div class="chart-container">
                <div class="chart-title">📈 Evolución de Producción: Crisis vs Meta</div>
                <canvas id="productionChart" width="400" height="300"></canvas>
            </div>

            <!-- Gráfico de Distribución de Costos -->
            <div class="chart-container">
                <div class="chart-title">💰 Distribución de Costos Operativos Diarios</div>
                <canvas id="costsChart" width="400" height="300"></canvas>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Análisis de Rentabilidad -->
            <div class="chart-container">
                <div class="chart-title">💎 Análisis de Rentabilidad Mensual</div>
                <canvas id="profitabilityChart" width="400" height="300"></canvas>
            </div>

            <!-- Impacto de Paradas Operativas -->
            <div class="chart-container">
                <div class="chart-title">⚠️ Costo de Paradas Operativas</div>
                <canvas id="downTimeChart" width="400" height="300"></canvas>
            </div>
        </div>

        <!-- Proyección de Indicadores KPI -->
        <div class="chart-container full-width">
            <div class="chart-title">📊 Indicadores KPI: Situación Actual vs Meta</div>
            <canvas id="kpiChart" width="400" height="300"></canvas>
        </div>

        <!-- Cronograma de Implementación -->
        <div class="implementation-timeline full-width">
            <div class="chart-title">🚀 Cronograma de Implementación</div>
            <div class="timeline-item">
                <div class="week-number">Sem 1</div>
                <div class="action-text">Abastecimiento stock insumos críticos + Reemplazo personal cocina</div>
                <div class="responsible">Gerencia o Logística o RR.HH.</div>
            </div>
            <div class="timeline-item">
                <div class="week-number">Sem 2</div>
                <div class="action-text">Dashboard KPI + Ajuste salarial (30%-50% reducción administrativa)</div>
                <div class="responsible">RR.HH & Gerencia</div>
            </div>
            <div class="timeline-item">
                <div class="week-number">Sem 3</div>
                <div class="action-text">Incremento disparos + Pago por tonelada + Instalación paneles solares</div>
                <div class="responsible">Operaciones</div>
            </div>
            <div class="timeline-item">
                <div class="week-number">Sem 4</div>
                <div class="action-text">Revisión de resultados y ajustes finales</div>
                <div class="responsible">Gerencia General</div>
            </div>
        </div>

        <!-- Conclusiones Estratégicas -->
        <div class="conclusions">
            <h3>🎯 Conclusiones Estratégicas</h3>
            <div class="conclusion-points">
                <div class="conclusion-point">
                    <h4>Sostenibilidad Garantizada</h4>
                    <p>Utilidad proyectada de <strong>$323,235 USD/mes</strong> con rentabilidad del <strong>67.1%</strong> (ley 3.5% + optimización integral)</p>
                </div>
                <div class="conclusion-point">
                    <h4>Factor Humano Crítico</h4>
                    <p>Mejora en alimentación genera <strong>$18,000 USD/mes</strong> adicionales en productividad. Inversión en bienestar = retorno directo en eficiencia</p>
                </div>
                <div class="conclusion-point">
                    <h4>Riesgo Controlado</h4>
                    <p>Cada hora de parada cuesta <strong>$538.73 USD</strong>. Protocolos de redundancia operativa y stock de seguridad implementados</p>
                </div>
                <div class="conclusion-point">
                    <h4>Reactivación Inmediata</h4>
                    <p>De <strong>6 TN/día</strong> a <strong>60 TN/día</strong> en 30 días. Inversión de <strong>S/201,644.80</strong> recuperable en el primer mes</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuración global de Chart.js
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.font.size = 12;

        // 1. Gráfico de Producción Comparativa
        const productionCtx = document.getElementById('productionChart').getContext('2d');
        new Chart(productionCtx, {
            type: 'bar',
            data: {
                labels: ['Abril 2025\n(Crisis)', 'Meta Junio 2025\n(Reactivación)'],
                datasets: [{
                    label: 'Producción Mensual (TN)',
                    data: [150, 1500],
                    backgroundColor: ['#e74c3c', '#2ecc71'],
                    borderColor: ['#c0392b', '#27ae60'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + ' TN (' + (context.parsed.y/25).toFixed(1) + ' TN/día)';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Toneladas' }
                    }
                }
            }
        });

        // 2. Gráfico de Distribución de Costos
        const costsCtx = document.getElementById('costsChart').getContext('2d');
        new Chart(costsCtx, {
            type: 'doughnut',
            data: {
                labels: ['Fulminantes', 'Emulnor', 'Petróleo', 'Nitrato', 'Mecha', 'Gasolina'],
                datasets: [{
                    data: [3200, 3136, 984, 374.4, 369.6, 87.6],
                    backgroundColor: [
                        '#e74c3c', '#3498db', '#f39c12', 
                        '#9b59b6', '#2ecc71', '#34495e'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'bottom' },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const percentage = ((context.parsed / 8151.6) * 100).toFixed(1);
                                return context.label + ': S/' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // 3. Análisis de Rentabilidad
        const profitabilityCtx = document.getElementById('profitabilityChart').getContext('2d');
        new Chart(profitabilityCtx, {
            type: 'bar',
            data: {
                labels: ['Ingresos', 'Costos', 'Utilidad Neta'],
                datasets: [{
                    data: [481530, 158295, 323235],
                    backgroundColor: ['#3498db', '#e74c3c', '#2ecc71'],
                    borderColor: ['#2980b9', '#c0392b', '#27ae60'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'USD (Miles)' },
                        ticks: {
                            callback: function(value) {
                                return '$' + (value/1000).toFixed(0) + 'K';
                            }
                        }
                    }
                }
            }
        });

        // 4. Impacto de Paradas
        const downTimeCtx = document.getElementById('downTimeChart').getContext('2d');
        new Chart(downTimeCtx, {
            type: 'line',
            data: {
                labels: ['1 hora', '6 horas', '12 horas', '24 horas'],
                datasets: [{
                    label: 'Pérdida Económica (USD)',
                    data: [538.73, 3232.38, 6464.70, 12929.40],
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    borderColor: '#e74c3c',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Pérdida (USD)' },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // 5. Indicadores KPI
        const kpiCtx = document.getElementById('kpiChart').getContext('2d');
        new Chart(kpiCtx, {
            type: 'radar',
            data: {
                labels: ['Producción Diaria', 'Taladros Productivos', 'Tiempo Reasignación', 'Ratio Mineral/Desmonte'],
                datasets: [
                    {
                        label: 'Situación Actual',
                        data: [10, 25, 20, 20], // Valores normalizados a 100
                        backgroundColor: 'rgba(231, 76, 60, 0.2)',
                        borderColor: '#e74c3c',
                        borderWidth: 2
                    },
                    {
                        label: 'Meta Reactivación',
                        data: [100, 60, 80, 67], // Valores normalizados a 100
                        backgroundColor: 'rgba(46, 204, 113, 0.2)',
                        borderColor: '#2ecc71',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'bottom' }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: { display: false }
                    }
                }
            }
        });


    </script>
</body>
</html>